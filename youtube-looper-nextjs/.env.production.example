# PRODUCTION ENVIRONMENT
# This file is for production deployment
# Copy this to .env.production and fill in your production values

# Environment
NODE_ENV=production
NEXT_PUBLIC_APP_ENV=production

# YouTube API Configuration
# Use a separate API key for production (recommended)
NEXT_PUBLIC_YOUTUBE_API_KEY=your_production_youtube_api_key_here

# Firebase Configuration (Production)
# Use a separate Firebase project for production
NEXT_PUBLIC_FIREBASE_API_KEY=your_production_firebase_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_production_project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_production_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_production_project.firebasestorage.app
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_production_messaging_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_production_app_id
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=your_production_measurement_id

# Production specific settings
NEXT_PUBLIC_DEBUG_MODE=false
NEXT_PUBLIC_API_BASE_URL=https://your-production-domain.com
