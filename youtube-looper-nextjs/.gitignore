# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# Firebase
firebase-config.json
config.json
.firebase/
firebase-debug.log
firestore-debug.log
ui-debug.log

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
Thumbs.db
