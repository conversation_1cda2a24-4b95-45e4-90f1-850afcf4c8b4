# ENVIRONMENT TEMPLATE
# Copy this file to .env.local for development or .env.production for production
# Fill in the actual values for your environment

# Environment (development/production)
NODE_ENV=development
NEXT_PUBLIC_APP_ENV=development

# YouTube API Configuration
# Get from: https://console.cloud.google.com/
# 1. Create a new project or select existing
# 2. Enable YouTube Data API v3
# 3. Create credentials (API key)
# 4. Restrict the API key (optional but recommended)
NEXT_PUBLIC_YOUTUBE_API_KEY=your_youtube_api_key_here

# Firebase Configuration
# Firebase config is now hardcoded in src/lib/firebase/config.ts
# No environment variables needed for Firebase - config is public anyway

# Application Settings
NEXT_PUBLIC_DEBUG_MODE=true
NEXT_PUBLIC_API_BASE_URL=http://localhost:3000

# Optional: Additional services (uncomment and configure as needed)
# NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX
# NEXT_PUBLIC_SENTRY_DSN=your_sentry_dsn_here
# NEXT_PUBLIC_HOTJAR_ID=your_hotjar_id_here
