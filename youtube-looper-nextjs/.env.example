# ENVIRONMENT TEMPLATE
# Copy this file to .env.local for development or .env.production for production
# Fill in the actual values for your environment

# Environment (development/production)
NODE_ENV=development
NEXT_PUBLIC_APP_ENV=development

# YouTube API Configuration
# Get from: https://console.cloud.google.com/
# 1. Create a new project or select existing
# 2. Enable YouTube Data API v3
# 3. Create credentials (API key)
# 4. Restrict the API key (optional but recommended)
NEXT_PUBLIC_YOUTUBE_API_KEY=your_youtube_api_key_here

# Firebase Configuration
# Get from: https://console.firebase.google.com/
# 1. Create a new project or select existing
# 2. Add a web app to your project
# 3. Copy the configuration values from the setup
NEXT_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key_here
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project_id.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project_id.firebasestorage.app
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_messaging_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id

# Application Settings
NEXT_PUBLIC_DEBUG_MODE=true
NEXT_PUBLIC_API_BASE_URL=http://localhost:3000

# Optional: Additional services (uncomment and configure as needed)
# NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX
# NEXT_PUBLIC_SENTRY_DSN=your_sentry_dsn_here
# NEXT_PUBLIC_HOTJAR_ID=your_hotjar_id_here
