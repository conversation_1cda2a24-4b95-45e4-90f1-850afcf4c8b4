# Environment Configuration Guide

This project supports multiple environments with different configurations for development and production.

## Environment Files

### `.env.local` (Development)
- Used for local development
- Contains development API keys and Firebase configuration
- **Never commit this file to version control**

### `.env.production` (Production)
- Contains production environment variables
- Used when building for production deployment
- **Never commit this file to version control**

### `.env.example` (Template)
- Template file showing all required environment variables
- Safe to commit to version control
- Copy this file to create your environment files

## Setup Instructions

### 1. Initial Setup
```bash
# Copy the example file to create your local environment
npm run env:copy

# Edit .env.local with your development values
# Edit .env.production with your production values (when ready)
```

### 2. Required Environment Variables

All environments need these variables:

```bash
# YouTube API
NEXT_PUBLIC_YOUTUBE_API_KEY=your_api_key_here

# Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project.firebasestorage.app
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id
```

### 3. Environment-Specific Variables

```bash
# Environment identifier
NEXT_PUBLIC_APP_ENV=development  # or 'production'

# Debug mode (true for development, false for production)
NEXT_PUBLIC_DEBUG_MODE=true

# API base URL
NEXT_PUBLIC_API_BASE_URL=http://localhost:3000  # or your production URL
```

## Build Commands

### Development Build
```bash
# Build for development
npm run build:dev

# Export static files for development
npm run export:dev
```

### Production Build
```bash
# Build for production
npm run build:prod

# Export static files for production
npm run export:prod
```

### Environment Validation
```bash
# Check if all required environment variables are present
npm run env:check
```

## Deployment Commands

### Firebase Deployment

```bash
# Deploy to production (uses production environment)
npm run firebase:deploy

# Deploy to development Firebase project
npm run firebase:deploy:dev

# Deploy to production Firebase project
npm run firebase:deploy:prod

# Deploy only hosting (faster)
npm run firebase:deploy:hosting:prod
```

## Environment Variable Naming

- **Public variables**: Must start with `NEXT_PUBLIC_` to be available in the browser
- **Server-only variables**: Don't use the `NEXT_PUBLIC_` prefix (for future server-side features)

## Security Best Practices

1. **Never commit environment files** (except `.env.example`)
2. **Use different Firebase projects** for development and production
3. **Restrict API keys** in Google Cloud Console
4. **Use different YouTube API keys** for development and production
5. **Enable Firebase security rules** appropriate for each environment

## Troubleshooting

### Missing Environment Variables
If you see warnings about missing environment variables:

1. Check that your `.env.local` file exists
2. Verify all required variables are set
3. Run `npm run env:check` to validate

### Build Failures
If builds fail due to environment issues:

1. Ensure you're using the correct build command for your target environment
2. Check that all required environment variables are present
3. Verify Firebase configuration is correct for the target environment

### Firebase Deployment Issues
If Firebase deployment fails:

1. Ensure you have the correct Firebase project selected
2. Check that your Firebase configuration matches your environment
3. Verify you have proper permissions for the target Firebase project

## Environment Differences

| Feature | Development | Production |
|---------|-------------|------------|
| Debug Mode | Enabled | Disabled |
| React Strict Mode | Enabled | Disabled |
| Compression | Disabled | Enabled |
| Source Maps | Enabled | Disabled |
| Firebase Project | Development | Production |
| API Quotas | Lower | Higher |

## Adding New Environment Variables

1. Add the variable to `.env.example`
2. Add it to your local `.env.local`
3. Add it to `.env.production`
4. Update `scripts/check-env.js` if it's required
5. Update `next.config.js` if it needs to be passed to the client
6. Update this documentation
