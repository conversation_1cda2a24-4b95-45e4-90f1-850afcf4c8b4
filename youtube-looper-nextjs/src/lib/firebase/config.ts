import { initializeApp, getApps, FirebaseApp } from 'firebase/app'
import { getFirestore, Firestore, connectFirestoreEmulator } from 'firebase/firestore'
import { getAuth, Auth, connectAuthEmulator } from 'firebase/auth'

// Firebase configuration interface
export interface FirebaseConfig {
  apiKey: string
  authDomain: string
  projectId: string
  storageBucket: string
  messagingSenderId: string
  appId: string
  measurementId?: string
}

// Firebase configuration for development environment
const firebaseConfig: FirebaseConfig = {
  apiKey: "AIzaSyDvaQjw6Wb8_mimsO4XnqCF4nuvRtaMaYg",
  authDomain: "tubli-dev.firebaseapp.com",
  projectId: "tubli-dev",
  storageBucket: "tubli-dev.firebasestorage.app",
  messagingSenderId: "328387300653",
  appId: "1:328387300653:web:290ebe6d7833528bee97a4",
  measurementId: "G-QWKHK7R38K"
}

// Initialize Firebase app
let app: FirebaseApp
let db: Firestore
let auth: Auth

export const initializeFirebase = (): { app: FirebaseApp; db: Firestore; auth: Auth } | null => {
  try {
    // Check if Firebase is already initialized
    if (getApps().length > 0) {
      app = getApps()[0]
    } else {
      // Initialize Firebase with hardcoded config
      app = initializeApp(firebaseConfig)
      console.log('✅ Firebase app initialized')
    }

    // Initialize Firestore
    db = getFirestore(app)
    
    // Initialize Auth
    auth = getAuth(app)

    // Connect to emulators in development (if needed)
    if (process.env.NODE_ENV === 'development' && process.env.NEXT_PUBLIC_USE_FIREBASE_EMULATOR === 'true') {
      try {
        connectFirestoreEmulator(db, 'localhost', 8080)
        connectAuthEmulator(auth, 'http://localhost:9099')
        console.log('🔧 Connected to Firebase emulators')
      } catch (error) {
        console.log('Firebase emulators already connected or not available')
      }
    }

    return { app, db, auth }
  } catch (error) {
    console.error('❌ Error initializing Firebase:', error)
    return null
  }
}

// Export Firebase instances
export const getFirebaseApp = (): FirebaseApp | null => app || null
export const getFirebaseDb = (): Firestore | null => db || null
export const getFirebaseAuth = (): Auth | null => auth || null

// Export configuration for external use
export { firebaseConfig }
