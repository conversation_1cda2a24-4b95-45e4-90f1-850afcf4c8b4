'use client'

import { useState, useRef, useCallback, useEffect } from 'react'
import { formatTime } from '@/lib/utils/time'

interface RangeSliderProps {
  min: number
  max: number
  startValue: number
  endValue: number
  onChange: (start: number, end: number) => void
  step?: number
  className?: string
  disabled?: boolean
}

export function RangeSlider({
  min,
  max,
  startValue,
  endValue,
  onChange,
  step = 1,
  className = '',
  disabled = false
}: RangeSliderProps) {
  const [isDragging, setIsDragging] = useState<'start' | 'end' | null>(null)
  const sliderRef = useRef<HTMLDivElement>(null)

  // Ensure values are within bounds
  const clampedStart = Math.max(min, Math.min(max - step, startValue))
  const clampedEnd = Math.max(clampedStart + step, Math.min(max, endValue))

  const getValueFromPosition = useCallback((clientX: number) => {
    if (!sliderRef.current) return min

    const rect = sliderRef.current.getBoundingClientRect()
    const percentage = Math.max(0, Math.min(1, (clientX - rect.left) / rect.width))
    const value = min + percentage * (max - min)
    
    // Round to nearest step
    return Math.round(value / step) * step
  }, [min, max, step])

  const handlePointerDown = useCallback((handle: 'start' | 'end') => (e: React.PointerEvent) => {
    if (disabled) return
    e.preventDefault()
    setIsDragging(handle)
    // Capture pointer for better touch handling
    e.currentTarget.setPointerCapture(e.pointerId)
  }, [disabled])

  const handlePointerMove = useCallback((e: PointerEvent) => {
    if (!isDragging || disabled) return

    const newValue = getValueFromPosition(e.clientX)

    if (isDragging === 'start') {
      const newStart = Math.min(newValue, clampedEnd - step)
      onChange(newStart, clampedEnd)
    } else {
      const newEnd = Math.max(newValue, clampedStart + step)
      onChange(clampedStart, newEnd)
    }
  }, [isDragging, disabled, getValueFromPosition, clampedStart, clampedEnd, step, onChange])

  const handlePointerUp = useCallback(() => {
    setIsDragging(null)
  }, [])

  // Handle pointer events (works for both mouse and touch)
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('pointermove', handlePointerMove)
      document.addEventListener('pointerup', handlePointerUp)
      return () => {
        document.removeEventListener('pointermove', handlePointerMove)
        document.removeEventListener('pointerup', handlePointerUp)
      }
    }
  }, [isDragging, handlePointerMove, handlePointerUp])

  // Calculate positions as percentages
  const startPercentage = ((clampedStart - min) / (max - min)) * 100
  const endPercentage = ((clampedEnd - min) / (max - min)) * 100

  const adjustStartTime = useCallback((delta: number) => {
    if (disabled) return
    const newStart = Math.max(min, Math.min(clampedEnd - step, clampedStart + delta))
    onChange(newStart, clampedEnd)
  }, [disabled, min, clampedEnd, step, clampedStart, onChange])

  const adjustEndTime = useCallback((delta: number) => {
    if (disabled) return
    const newEnd = Math.max(clampedStart + step, Math.min(max, clampedEnd + delta))
    onChange(clampedStart, newEnd)
  }, [disabled, max, clampedStart, step, clampedEnd, onChange])

  return (
    <div className={`relative ${className}`}>
      {/* Time labels */}
      <div className="flex justify-between text-xs text-dark-400 mb-2">
        <span>{formatTime(min)}</span>
        <span>{formatTime(max)}</span>
      </div>

      {/* Slider track */}
      <div
        ref={sliderRef}
        className={`relative h-6 bg-dark-700 rounded-lg cursor-pointer ${
          disabled ? 'opacity-50 cursor-not-allowed' : ''
        }`}
        onPointerDown={(e) => {
          if (disabled) return
          const newValue = getValueFromPosition(e.clientX)
          const startDistance = Math.abs(newValue - clampedStart)
          const endDistance = Math.abs(newValue - clampedEnd)

          if (startDistance < endDistance) {
            const newStart = Math.min(newValue, clampedEnd - step)
            onChange(newStart, clampedEnd)
          } else {
            const newEnd = Math.max(newValue, clampedStart + step)
            onChange(clampedStart, newEnd)
          }
        }}
      >
        {/* Selected range */}
        <div
          className="absolute top-0 h-full bg-primary-500/30 rounded-lg"
          style={{
            left: `${startPercentage}%`,
            width: `${endPercentage - startPercentage}%`
          }}
        />

        {/* Start handle */}
        <div
          className={`absolute top-1/2 w-4 h-4 bg-primary-500 border-2 border-white rounded-full transform -translate-y-1/2 cursor-grab shadow-lg transition-all touch-none ${
            isDragging === 'start' ? 'scale-110 cursor-grabbing' : 'hover:scale-105'
          } ${disabled ? 'cursor-not-allowed' : ''}`}
          style={{ left: `calc(${startPercentage}% - 8px)` }}
          onPointerDown={handlePointerDown('start')}
        />

        {/* End handle */}
        <div
          className={`absolute top-1/2 w-4 h-4 bg-primary-500 border-2 border-white rounded-full transform -translate-y-1/2 cursor-grab shadow-lg transition-all touch-none ${
            isDragging === 'end' ? 'scale-110 cursor-grabbing' : 'hover:scale-105'
          } ${disabled ? 'cursor-not-allowed' : ''}`}
          style={{ left: `calc(${endPercentage}% - 8px)` }}
          onPointerDown={handlePointerDown('end')}
        />
      </div>

      {/* Current values with precision controls */}
      <div className="flex justify-between items-center text-xs mt-3">
        <div className="flex items-center gap-2">
          <div className="text-primary-300">
            <span className="text-dark-400">Start: </span>
            <span className="font-mono">{formatTime(clampedStart)}</span>
          </div>
          <div className="flex items-center gap-1">
            <button
              onClick={() => adjustStartTime(-step)}
              disabled={disabled || clampedStart <= min}
              className={`w-6 h-6 rounded flex items-center justify-center text-xs font-bold transition-colors ${
                disabled || clampedStart <= min
                  ? 'bg-dark-700 text-dark-500 cursor-not-allowed'
                  : 'bg-dark-600 hover:bg-dark-500 text-dark-200 hover:text-white'
              }`}
              title="Decrease start time by 1 second"
            >
              −
            </button>
            <button
              onClick={() => adjustStartTime(step)}
              disabled={disabled || clampedStart >= clampedEnd - step}
              className={`w-6 h-6 rounded flex items-center justify-center text-xs font-bold transition-colors ${
                disabled || clampedStart >= clampedEnd - step
                  ? 'bg-dark-700 text-dark-500 cursor-not-allowed'
                  : 'bg-dark-600 hover:bg-dark-500 text-dark-200 hover:text-white'
              }`}
              title="Increase start time by 1 second"
            >
              +
            </button>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-1">
            <button
              onClick={() => adjustEndTime(-step)}
              disabled={disabled || clampedEnd <= clampedStart + step}
              className={`w-6 h-6 rounded flex items-center justify-center text-xs font-bold transition-colors ${
                disabled || clampedEnd <= clampedStart + step
                  ? 'bg-dark-700 text-dark-500 cursor-not-allowed'
                  : 'bg-dark-600 hover:bg-dark-500 text-dark-200 hover:text-white'
              }`}
              title="Decrease end time by 1 second"
            >
              −
            </button>
            <button
              onClick={() => adjustEndTime(step)}
              disabled={disabled || clampedEnd >= max}
              className={`w-6 h-6 rounded flex items-center justify-center text-xs font-bold transition-colors ${
                disabled || clampedEnd >= max
                  ? 'bg-dark-700 text-dark-500 cursor-not-allowed'
                  : 'bg-dark-600 hover:bg-dark-500 text-dark-200 hover:text-white'
              }`}
              title="Increase end time by 1 second"
            >
              +
            </button>
          </div>
          <div className="text-primary-300">
            <span className="text-dark-400">End: </span>
            <span className="font-mono">{formatTime(clampedEnd)}</span>
          </div>
        </div>
      </div>
    </div>
  )
}
