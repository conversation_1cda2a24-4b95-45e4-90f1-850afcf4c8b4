'use client'

import { useState, FormEvent } from 'react'
import { usePasswordGate } from '@/hooks/usePasswordGate'

export function PasswordGate() {
  const [password, setPassword] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const { verifyPassword } = usePasswordGate()

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault()
    if (!password.trim()) {
      setError('Please enter a password')
      return
    }

    setIsLoading(true)
    setError('')

    try {
      const isValid = await verifyPassword(password)
      if (!isValid) {
        setError('Incorrect password. Please try again.')
        setPassword('')
      }
    } catch (error) {
      setError('An error occurred. Please try again.')
      console.error('Password verification error:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-dark-900 via-dark-800 to-dark-900 flex items-center justify-center px-4 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-primary-500 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-primary-600 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 max-w-md w-full">
        {/* Main card */}
        <div className="glassmorphism-strong p-8 space-y-8">
          {/* Header with logo and branding */}
          <div className="text-center space-y-4">
            {/* Logo */}
            <div className="flex justify-center mb-4">
              <div className="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center shadow-2xl">
                <svg
                  width="32"
                  height="32"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                  className="text-white"
                >
                  <path d="M8 5v14l11-7z"/>
                </svg>
              </div>
            </div>

            {/* App name and tagline */}
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-white via-primary-200 to-primary-300 bg-clip-text text-transparent mb-2">
                Tubli
              </h1>
              <p className="text-primary-300 text-lg font-medium mb-2">
                YouTube Queue & Looper
              </p>
              <div className="inline-flex items-center px-3 py-1 rounded-full bg-primary-500/20 border border-primary-400/30">
                <span className="text-primary-300 text-sm font-medium">Early Access</span>
              </div>
            </div>

            {/* Description */}
            <p className="text-dark-300 text-sm leading-relaxed">
              Create, share, and loop YouTube video queues with advanced timeframe controls.
              This is an early access version with ongoing development.
            </p>
          </div>

          {/* Access form */}
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-dark-200 mb-2">
                Access Password
              </label>
              <input
                id="password"
                name="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter your access password"
                className="w-full px-4 py-3 bg-dark-800/50 border border-dark-600/50 rounded-xl text-white placeholder-dark-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200 backdrop-blur-sm"
                disabled={isLoading}
                autoFocus
              />
            </div>

            {error && (
              <div className="p-3 rounded-lg bg-red-500/10 border border-red-500/20 text-red-400 text-sm text-center">
                {error}
              </div>
            )}

            <button
              type="submit"
              disabled={isLoading}
              className="w-full py-3 px-4 bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 disabled:from-primary-800 disabled:to-primary-900 disabled:cursor-not-allowed text-white font-medium rounded-xl transition-all duration-200 flex items-center justify-center shadow-lg hover:shadow-primary-500/25 transform hover:scale-[1.02] disabled:transform-none"
            >
              {isLoading ? (
                <>
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2"></div>
                  Verifying Access...
                </>
              ) : (
                <>
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" className="mr-2">
                    <path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4z"/>
                  </svg>
                  Enter Application
                </>
              )}
            </button>
          </form>

          {/* Footer info */}
          <div className="text-center space-y-2">
            <p className="text-dark-400 text-xs">
              Secure access required for early testing phase
            </p>
            {process.env.NODE_ENV === 'development' && (
              <p className="text-dark-500 text-xs">
                Development Mode • Password stored in Firebase
              </p>
            )}
          </div>
        </div>

        {/* Version info */}
        <div className="text-center mt-6">
          <p className="text-dark-500 text-xs">
            Version 0.1.0
          </p>
        </div>
      </div>
    </div>
  )
}
